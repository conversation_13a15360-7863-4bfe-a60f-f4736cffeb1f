"use server";

import { unstable_cache, revalidateTag } from "next/cache";
import {
  List,
  createList,
  deleteList,
  getListsByUserId,
  getListById,
  updateList,
  getDefaultListForUser,
  reorderLists,
  getTaskCountsByUserId
} from "@/lib/db";

export const fetchLists = unstable_cache(
  async (userId: string): Promise<List[]> => {
    const lists = await getListsByUserId(userId);
    return lists;
  },
  ["lists-jwt"],
  {
    tags: ["lists"],
    revalidate: 600, // 10 minutes
  }
);

export async function fetchList(listId: string): Promise<List | null> {
  return getListById(listId);
}

export async function addList(userId: string, spaceId: string, name: string, color?: string | null, description?: string | null): Promise<List | null> {
  console.log("Server action: addList called with userId:", userId, "spaceId:", spaceId, "name:", name, "color:", color, "description:", description);
  const list = await createList(userId, spaceId, name, color, description);

  // Invalidate cache when list is added
  revalidateTag("lists");
  revalidateTag("spaces"); // Also invalidate spaces cache for list counts

  console.log("Server action: addList returning:", list);
  return list;
}

export async function editList(
  listId: string,
  userId: string,
  data: { name?: string; description?: string | null; color?: string | null }
): Promise<List | null> {
  console.log("Server action: editList called with listId:", listId, "userId:", userId, "data:", data);
  const list = await updateList(listId, userId, data);

  // Invalidate cache when list is updated
  revalidateTag("lists");

  console.log("Server action: editList returning:", list);
  return list;
}

export async function removeList(listId: string, userId: string): Promise<boolean> {
  console.log("Server action: removeList called with listId:", listId, "userId:", userId);
  const success = await deleteList(listId, userId);

  // Invalidate cache when list is deleted
  revalidateTag("lists");
  revalidateTag("tasks"); // Tasks in the deleted list are also affected

  console.log("Server action: removeList returning:", success);
  return success;
}

export const fetchDefaultList = unstable_cache(
  async (userId: string): Promise<List | null> => {
    console.log("Server action: fetchDefaultList called with userId:", userId);
    const list = await getDefaultListForUser(userId);
    console.log("Server action: fetchDefaultList returning:", list);
    return list;
  },
  ["default-list-jwt"],
  {
    tags: ["lists"],
    revalidate: 600, // 10 minutes
  }
);

export async function reorderUserLists(userId: string, listIds: string[]): Promise<boolean> {
  console.log("Server action: reorderUserLists called with userId:", userId, "listIds:", listIds);
  const success = await reorderLists(userId, listIds);

  // Invalidate cache when list order is updated
  revalidateTag("lists");

  console.log("Server action: reorderUserLists returning:", success);
  return success;
}

export const fetchTaskCounts = unstable_cache(
  async (userId: string): Promise<Record<string, number>> => {
    console.log("Server action: fetchTaskCounts called with userId:", userId);
    const counts = await getTaskCountsByUserId(userId);
    console.log("Server action: fetchTaskCounts returning:", counts);
    return counts;
  },
  ["task-counts-jwt"],
  {
    tags: ["lists", "tasks"],
    revalidate: 600, // 10 minutes
  }
);
