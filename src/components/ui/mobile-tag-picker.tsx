"use client";

import * as React from "react";
import { Plus, Search, Tag as TagIcon, ChevronLeft, List } from "lucide-react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { TagPill } from "@/components/ui/tag-pill";
import { ColorPicker } from "@/components/ui/color-picker";
import { getRandomTagColor } from "@/lib/tag-colors";
import { useSmartTagSearch } from "@/hooks/use-smart-tag-search";
import { useMediaQuery } from "@/hooks/use-media-query";
import {
  MobileDialog,
  MobileDialogContent,
  MobileDialogHeader,
  MobileDialogTitle,
} from "@/components/ui/mobile-dialog";
import { StackedMobileDialog, StackedMobileDialogContent } from "@/components/ui/stacked-mobile-dialog";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { CreateTagDialog } from "@/components/ui/create-tag-dialog";
import type { Tag } from "@/lib/db";
import type { CreateTagData } from "@/lib/types";

export interface MobileTagPickerProps {
  selectedTags: Tag[];
  availableTags: Tag[];
  onTagSelect: (tag: Tag) => void;
  onTagRemove: (tag: Tag) => void;
  onTagCreate: (name: string, color: string) => Promise<Tag | null>;
  onSearchTags: (searchTerm: string) => Promise<Tag[]>;
  className?: string;
  disabled?: boolean;
  placeholder?: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  isStackedModal?: boolean; // New prop to indicate this is opening on top of another modal
  // Extended functionality for picklist tags
  onCreatePicklistTag?: (data: CreateTagData) => Promise<Tag | null>;
}

export function MobileTagPicker({
  selectedTags,
  availableTags,
  onTagSelect,
  onTagRemove,
  onTagCreate,
  onSearchTags,
  className,
  disabled = false,
  placeholder = "Search or create tags...",
  open,
  onOpenChange,
  isStackedModal = false,
  onCreatePicklistTag,
}: MobileTagPickerProps) {
  const [isCreating, setIsCreating] = React.useState(false);
  const [newTagColor, setNewTagColor] = React.useState<string>(getRandomTagColor().value);
  const [showExpandedTags, setShowExpandedTags] = React.useState(false);
  const [showCreateTagDialog, setShowCreateTagDialog] = React.useState(false);
  const searchInputRef = React.useRef<HTMLInputElement>(null);

  // Use smart search hook for optimal performance
  const { filteredTags, isSearching, searchTerm, setSearchTerm } = useSmartTagSearch({
    availableTags,
    selectedTags,
    onSearchTags,
    clientSearchThreshold: 2,
    debounceMs: 300,
  });

  // Note: Removed auto-focus to prevent virtual keyboard from appearing automatically

  // Reset state when modal closes
  React.useEffect(() => {
    if (!open) {
      setSearchTerm("");
      setIsCreating(false);
      setShowExpandedTags(false);
      setShowCreateTagDialog(false);
      setNewTagColor(getRandomTagColor().value);
    }
  }, [open, setSearchTerm]);

  const handleTagSelect = (tag: Tag) => {
    onTagSelect(tag);
    setSearchTerm("");
    setIsCreating(false);
  };

  const handleCreateTag = async () => {
    if (!searchTerm.trim()) return;

    setIsCreating(true);
    try {
      const newTag = await onTagCreate(searchTerm.trim(), newTagColor);
      if (newTag) {
        onTagSelect(newTag);
        setSearchTerm("");
        setNewTagColor(getRandomTagColor().value);
      }
    } catch (error) {
      console.error('Error creating tag:', error);
    } finally {
      setIsCreating(false);
    }
  };

  const handleCreateTagFromDialog = async (data: CreateTagData): Promise<boolean> => {
    console.log('MobileTagPicker: handleCreateTagFromDialog called with:', data);
    console.log('MobileTagPicker: onCreatePicklistTag available:', !!onCreatePicklistTag);

    try {
      let newTag: Tag | null = null;

      if (data.type === 'picklist' && onCreatePicklistTag) {
        console.log('MobileTagPicker: Creating picklist tag');
        newTag = await onCreatePicklistTag(data);
      } else {
        console.log('MobileTagPicker: Creating regular tag');
        // Regular tag creation
        newTag = await onTagCreate(data.name, data.color);
      }

      console.log('MobileTagPicker: Created tag:', newTag);

      if (newTag) {
        onTagSelect(newTag);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error creating tag from dialog:', error);
      return false;
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && searchTerm.trim()) {
      e.preventDefault();

      // If there's an exact match, select it
      const exactMatch = filteredTags.find(
        tag => tag.name.toLowerCase() === searchTerm.toLowerCase()
      );

      if (exactMatch) {
        handleTagSelect(exactMatch);
      } else {
        // Create new tag
        handleCreateTag();
      }
    }
  };

  // Determine if we should show create option
  const showCreateOption = searchTerm.trim() && 
    !filteredTags.some(tag => tag.name.toLowerCase() === searchTerm.toLowerCase()) &&
    !isSearching;

  // Split tags into visible and expandable sections
  const visibleTagLimit = 20;
  const visibleTags = filteredTags.slice(0, visibleTagLimit);
  const expandableTags = filteredTags.slice(visibleTagLimit);
  const hasExpandableTags = expandableTags.length > 0;

  // Use stacked modal for better layering when opened from another modal
  const DialogComponent = isStackedModal ? StackedMobileDialog : MobileDialog;
  const DialogContentComponent = isStackedModal ? StackedMobileDialogContent : MobileDialogContent;

  return (
    <>
      <DialogComponent open={open} onOpenChange={onOpenChange}>
      <DialogContentComponent
        className="p-0 overflow-hidden" // Override default overflow-y-auto to prevent top section scrolling
        fullHeight={true}
        enableSwipeToDismiss={true}
      >
        <MobileDialogHeader className="sr-only">
          <VisuallyHidden>
            <MobileDialogTitle>Select Tags</MobileDialogTitle>
          </VisuallyHidden>
        </MobileDialogHeader>

        <div className="flex flex-col h-full">
          {/* Fixed Header - No scrolling allowed in this section */}
          <div className="flex-shrink-0 bg-background">
            {/* Back Button */}
            <div className="flex items-start justify-start px-4 pt-4 pb-0">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => onOpenChange(false)}
                className="h-8 w-8 p-0"
              >
                <ChevronLeft className="h-6 w-6" />
                <span className="sr-only">Back</span>
              </Button>
            </div>

            {/* Title and Search */}
            <div className="p-4 border-b">
              <div className="flex items-center gap-3 mb-4">
                <TagIcon className="h-5 w-5 text-muted-foreground" />
                <h2 className="text-lg font-semibold">Tags</h2>
              </div>

              {/* Search Input - Fixed position */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  ref={searchInputRef}
                  type="text"
                  placeholder={placeholder}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyDown={handleKeyDown}
                  disabled={disabled}
                  className="pl-10 pr-4"
                  autoFocus={false}
                  tabIndex={-1}
                  onFocus={(e) => {
                    // Re-enable tabIndex when user manually focuses
                    e.target.tabIndex = 0;
                  }}
                />
              </div>
            </div>

            {/* Selected Tags - Part of fixed header, no scrolling */}
            {selectedTags.length > 0 && (
              <div className="p-4 border-b bg-background">
                <h3 className="text-sm font-medium text-muted-foreground mb-3">Selected</h3>
                <div className="flex flex-wrap gap-2">
                  {selectedTags.map((tag) => (
                    <TagPill
                      key={tag.id}
                      tag={tag}
                      onRemove={() => onTagRemove(tag)}
                      size="sm"
                      showRemove={true}
                    />
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Scrollable Content - Prevent swipe-to-dismiss when scrolling */}
          <div
            className="flex-1 overflow-y-auto pb-6" // Added bottom padding to prevent last item cutoff
            style={{ touchAction: 'pan-y' }} // Allow vertical scrolling, prevent swipe gestures
          >

            {/* Create New Tag Option */}
            {showCreateOption && (
              <div className="p-4 border-b space-y-3">
                {/* Quick Create */}
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.2 }}
                  className="flex items-center gap-3 p-3 rounded-lg border border-dashed border-muted-foreground/30 hover:border-muted-foreground/50 transition-colors"
                >
                  <ColorPicker
                    value={newTagColor}
                    onChange={setNewTagColor}
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    className="flex-1 justify-start h-auto p-0 font-normal"
                    onClick={handleCreateTag}
                    disabled={isCreating}
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Create "{searchTerm}"
                  </Button>
                </motion.div>

                {/* Advanced Create Button */}
                {onCreatePicklistTag && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2, delay: 0.1 }}
                  >
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full justify-start h-auto p-3"
                      onClick={() => setShowCreateTagDialog(true)}
                      disabled={disabled}
                    >
                      <List className="mr-2 h-4 w-4" />
                      Create Advanced Tag
                    </Button>
                  </motion.div>
                )}
              </div>
            )}

            {/* Available Tags */}
            {filteredTags.length > 0 && (
              <div className="p-4">
                <h3 className="text-sm font-medium text-muted-foreground mb-3">
                  {searchTerm ? 'Search Results' : 'Available Tags'}
                </h3>

                {/* Visible Tags */}
                <div className="space-y-2">
                  {visibleTags.map((tag) => (
                    <motion.div
                      key={tag.id}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Button
                        variant="ghost"
                        size="sm"
                        className="w-full justify-start h-auto p-3 hover:bg-muted/50"
                        onClick={() => handleTagSelect(tag)}
                        disabled={disabled}
                      >
                        <div
                          className="w-3 h-3 rounded-full mr-3 flex-shrink-0"
                          style={{ backgroundColor: tag.color }}
                        />
                        <span className="text-left flex-1">{tag.name}</span>
                      </Button>
                    </motion.div>
                  ))}
                </div>

                {/* Expandable Tags Section */}
                {hasExpandableTags && !searchTerm && (
                  <div className="mt-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full justify-center text-muted-foreground hover:text-foreground"
                      onClick={() => setShowExpandedTags(!showExpandedTags)}
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      {showExpandedTags ? 'Show Less' : `+ ${expandableTags.length} More Tags`}
                    </Button>

                    {/* Expanded Tags */}
                    {showExpandedTags && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.3 }}
                        className="mt-3 space-y-2"
                      >
                        {expandableTags.map((tag) => (
                          <motion.div
                            key={tag.id}
                            initial={{ opacity: 0, x: -10 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.2 }}
                          >
                            <Button
                              variant="ghost"
                              size="sm"
                              className="w-full justify-start h-auto p-3 hover:bg-muted/50"
                              onClick={() => handleTagSelect(tag)}
                              disabled={disabled}
                            >
                              <div
                                className="w-3 h-3 rounded-full mr-3 flex-shrink-0"
                                style={{ backgroundColor: tag.color }}
                              />
                              <span className="text-left flex-1">{tag.name}</span>
                            </Button>
                          </motion.div>
                        ))}
                      </motion.div>
                    )}
                  </div>
                )}
              </div>
            )}

            {/* Empty State */}
            {filteredTags.length === 0 && !showCreateOption && !isSearching && (
              <div className="p-8 text-center">
                <TagIcon className="h-12 w-12 text-muted-foreground/50 mx-auto mb-4" />
                <p className="text-muted-foreground">
                  {searchTerm ? 'No tags found' : 'No tags available'}
                </p>
                {searchTerm && (
                  <p className="text-sm text-muted-foreground/70 mt-1">
                    Try a different search term
                  </p>
                )}
              </div>
            )}

            {/* Loading State */}
            {isSearching && (
              <div className="p-8 text-center">
                <div className="animate-spin h-6 w-6 border-2 border-muted-foreground border-t-transparent rounded-full mx-auto mb-4" />
                <p className="text-muted-foreground">Searching tags...</p>
              </div>
            )}
          </div>
        </div>
      </DialogContentComponent>
    </DialogComponent>

    {/* Create Tag Dialog */}
    {onCreatePicklistTag && (
      <CreateTagDialog
        open={showCreateTagDialog}
        onOpenChange={setShowCreateTagDialog}
        onTagCreate={handleCreateTagFromDialog}
        disabled={disabled}
        isStackedModal={true}
      />
    )}
    </>
  );
}
