"use client";

import * as React from "react";
import { ChevronLeft, Plus, X, GripVertical } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  MobileDialog,
  MobileDialogContent,
  MobileDialogHeader,
  MobileDialogTitle,
} from "@/components/ui/mobile-dialog";
import { StackedMobileDialog, StackedMobileDialogContent } from "@/components/ui/stacked-mobile-dialog";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { ColorPicker } from "@/components/ui/color-picker";
import { getRandomTagColor } from "@/lib/tag-colors";
import type { CreateTagData, TagType } from "@/lib/types";

export interface CreateTagDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onTagCreate: (data: CreateTagData) => Promise<boolean>;
  initialName?: string;
  disabled?: boolean;
  isStackedModal?: boolean;
}

export function CreateTagDialog({
  open,
  onOpenChange,
  onTagCreate,
  initialName = "",
  disabled = false,
  isStackedModal = false,
}: CreateTagDialogProps) {
  const [name, setName] = React.useState(initialName);
  const [color, setColor] = React.useState<string>(getRandomTagColor().value);
  const [tagType, setTagType] = React.useState<TagType>('regular');
  const [picklistValues, setPicklistValues] = React.useState<string[]>(['']);
  const [isCreating, setIsCreating] = React.useState(false);
  const [error, setError] = React.useState("");

  // Reset form when dialog opens/closes
  React.useEffect(() => {
    if (open) {
      setName(initialName);
      setColor(getRandomTagColor().value);
      setTagType('regular');
      setPicklistValues(['']);
      setError("");
    }
  }, [open, initialName]);

  const handleClose = () => {
    if (!isCreating) {
      onOpenChange(false);
    }
  };

  const handleTagTypeChange = (type: TagType) => {
    setTagType(type);
    if (type === 'picklist' && picklistValues.length === 0) {
      setPicklistValues(['']);
    }
  };

  const handlePicklistValueChange = (index: number, value: string) => {
    const newValues = [...picklistValues];
    newValues[index] = value;
    setPicklistValues(newValues);
  };

  const handleAddPicklistValue = () => {
    setPicklistValues([...picklistValues, '']);
  };

  const handleRemovePicklistValue = (index: number) => {
    if (picklistValues.length > 1) {
      const newValues = picklistValues.filter((_, i) => i !== index);
      setPicklistValues(newValues);
    }
  };

  const handleCreate = async () => {
    if (!name.trim()) {
      setError("Tag name is required");
      return;
    }

    if (tagType === 'picklist') {
      const validValues = picklistValues.filter(v => v.trim());
      if (validValues.length === 0) {
        setError("At least one picklist value is required");
        return;
      }
    }

    setIsCreating(true);
    setError("");

    try {
      const tagData: CreateTagData = tagType === 'picklist' 
        ? {
            name: name.trim(),
            color,
            type: 'picklist',
            initialValues: picklistValues.filter(v => v.trim()),
          }
        : {
            name: name.trim(),
            color,
            type: 'regular',
          };

      const success = await onTagCreate(tagData);
      if (success) {
        onOpenChange(false);
      } else {
        setError("Failed to create tag. Tag name may already exist.");
      }
    } catch (error) {
      console.error("Error creating tag:", error);
      setError("Failed to create tag");
    } finally {
      setIsCreating(false);
    }
  };

  const DialogComponent = isStackedModal ? StackedMobileDialog : MobileDialog;
  const DialogContentComponent = isStackedModal ? StackedMobileDialogContent : MobileDialogContent;

  return (
    <DialogComponent open={open} onOpenChange={onOpenChange}>
      <DialogContentComponent className="sm:max-w-[425px]" fullHeight>
        <VisuallyHidden asChild>
          <MobileDialogTitle>Create Tag</MobileDialogTitle>
        </VisuallyHidden>
        
        <MobileDialogHeader className="flex items-start justify-start px-4 pt-4 pb-0">
          <Button
            variant="ghost"
            size="icon"
            onClick={handleClose}
            className="h-8 w-8 p-0"
            disabled={isCreating}
          >
            <ChevronLeft className="h-6 w-6" />
            <span className="sr-only">Back</span>
          </Button>
        </MobileDialogHeader>

        <div className="flex-1 overflow-y-auto px-4 pb-4 space-y-6">
          {/* Tag Name */}
          <div className="space-y-2">
            <Label htmlFor="tag-name">Tag Name</Label>
            <Input
              id="tag-name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Enter tag name"
              disabled={isCreating}
              autoComplete="off"
              spellCheck="false"
            />
          </div>

          {/* Color Picker */}
          <div className="space-y-2">
            <Label>Color</Label>
            <div className="flex items-center gap-3">
              <ColorPicker
                value={color}
                onChange={setColor}
                disabled={isCreating}
              />
              <span className="text-sm text-muted-foreground">
                Choose a color for this tag
              </span>
            </div>
          </div>

          {/* Tag Type Selection */}
          <div className="space-y-3">
            <Label>Tag Type</Label>
            <div className="space-y-2">
              <button
                type="button"
                onClick={() => handleTagTypeChange('regular')}
                disabled={isCreating}
                className={cn(
                  "w-full p-4 rounded-lg border text-left transition-colors",
                  tagType === 'regular'
                    ? "border-primary/30 bg-primary/5"
                    : "border-border hover:bg-muted/50",
                  isCreating && "opacity-50 cursor-not-allowed"
                )}
              >
                <div className="font-medium">Regular Tag</div>
                <div className="text-sm text-muted-foreground">
                  A simple tag that can be added to tasks
                </div>
              </button>

              <button
                type="button"
                onClick={() => handleTagTypeChange('picklist')}
                disabled={isCreating}
                className={cn(
                  "w-full p-4 rounded-lg border text-left transition-colors",
                  tagType === 'picklist'
                    ? "border-primary/30 bg-primary/5"
                    : "border-border hover:bg-muted/50",
                  isCreating && "opacity-50 cursor-not-allowed"
                )}
              >
                <div className="font-medium">Picklist Tag</div>
                <div className="text-sm text-muted-foreground">
                  A tag with predefined values that users can select from
                </div>
              </button>
            </div>
          </div>

          {/* Picklist Values */}
          {tagType === 'picklist' && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label>Picklist Values</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleAddPicklistValue}
                  disabled={isCreating}
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Add Value
                </Button>
              </div>
              
              <div className="space-y-2">
                {picklistValues.map((value, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <GripVertical className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                    <Input
                      value={value}
                      onChange={(e) => handlePicklistValueChange(index, e.target.value)}
                      placeholder={`Value ${index + 1}`}
                      disabled={isCreating}
                      className="flex-1"
                    />
                    {picklistValues.length > 1 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemovePicklistValue(index)}
                        disabled={isCreating}
                        className="p-1 h-8 w-8"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="text-sm text-destructive bg-destructive/10 p-3 rounded-lg">
              {error}
            </div>
          )}
        </div>

        {/* Create Button */}
        <div className="px-4 pb-4 border-t bg-background">
          <Button
            onClick={handleCreate}
            disabled={isCreating || !name.trim()}
            className="w-full mt-4"
          >
            {isCreating ? "Creating..." : "Create Tag"}
          </Button>
        </div>
      </DialogContentComponent>
    </DialogComponent>
  );
}
