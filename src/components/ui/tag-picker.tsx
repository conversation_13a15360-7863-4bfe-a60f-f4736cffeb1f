"use client";

import * as React from "react";
import { Plus, Search, Tag as TagIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { TagPill } from "@/components/ui/tag-pill";
import { ColorPicker } from "@/components/ui/color-picker";
import { getRandomTagColor } from "@/lib/tag-colors";
import { useSmartTagSearch } from "@/hooks/use-smart-tag-search";
import { useMediaQuery } from "@/hooks/use-media-query";
import { MobileTagPicker } from "@/components/ui/mobile-tag-picker";
import type { Tag } from "@/lib/db";

export interface TagPickerProps {
  selectedTags: Tag[];
  availableTags: Tag[];
  onTagSelect: (tag: Tag) => void;
  onTagRemove: (tag: Tag) => void;
  onTagCreate: (name: string, color: string) => Promise<Tag | null>;
  onSearchTags: (searchTerm: string) => Promise<Tag[]>;
  className?: string;
  disabled?: boolean;
  placeholder?: string;
  hideSelectedTags?: boolean; // Hide the display of selected tags
  // Extended functionality for picklist tags
  onCreatePicklistTag?: (name: string, color: string, initialValues: string[]) => Promise<Tag | null>;
  onPicklistValueSelect?: (tagId: string, valueId: string) => Promise<boolean>;
  onPicklistValueClear?: (tagId: string) => Promise<boolean>;
  getPicklistValues?: (tagId: string) => Promise<any[]>;
  getSelectedPicklistValue?: (tagId: string) => Promise<any | null>;
}

export function TagPicker({
  selectedTags,
  availableTags,
  onTagSelect,
  onTagRemove,
  onTagCreate,
  onSearchTags,
  className,
  disabled = false,
  placeholder = "Search or create tags...",
  hideSelectedTags = false,
}: TagPickerProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const [isCreating, setIsCreating] = React.useState(false);
  const [newTagColor, setNewTagColor] = React.useState<string>(getRandomTagColor().value);
  const isDesktop = useMediaQuery("(min-width: 768px)");

  // Use smart search hook for optimal performance
  const { filteredTags, isSearching, searchTerm, setSearchTerm } = useSmartTagSearch({
    availableTags,
    selectedTags,
    onSearchTags,
    clientSearchThreshold: 2, // Use client search for 1-2 characters
    debounceMs: 300,
  });



  const handleTagSelect = (tag: Tag) => {
    onTagSelect(tag);
    setSearchTerm("");
    setIsCreating(false);
  };

  const handleCreateTag = async () => {
    if (!searchTerm.trim()) return;

    setIsCreating(true);
    try {
      const newTag = await onTagCreate(searchTerm.trim(), newTagColor);
      if (newTag) {
        onTagSelect(newTag);
        setSearchTerm("");
        setNewTagColor(getRandomTagColor().value);
      }
    } catch (error) {
      console.error('Error creating tag:', error);
    } finally {
      setIsCreating(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && searchTerm.trim()) {
      e.preventDefault();

      // If there's an exact match, select it
      const exactMatch = filteredTags.find(
        tag => tag.name.toLowerCase() === searchTerm.toLowerCase()
      );

      if (exactMatch) {
        handleTagSelect(exactMatch);
      } else {
        // Create new tag
        handleCreateTag();
      }
    }
  };

  const showCreateOption = searchTerm.trim() &&
    !filteredTags.some(tag => tag.name.toLowerCase() === searchTerm.toLowerCase());

  // Mobile version
  if (!isDesktop) {
    return (
      <div className={cn("space-y-2", className)}>
        {/* Selected Tags */}
        {!hideSelectedTags && selectedTags.length > 0 && (
          <div className="flex flex-wrap gap-1.5">
            {selectedTags.map((tag) => (
              <TagPill
                key={tag.id}
                tag={tag}
                onRemove={() => onTagRemove(tag)}
                size="sm"
                showRemove={true}
                allowInlineEdit={false}
              />
            ))}
          </div>
        )}

        {/* Mobile Tag Trigger */}
        <Button
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal",
            selectedTags.length === 0 && "text-muted-foreground",
            disabled && "opacity-50 cursor-not-allowed"
          )}
          disabled={disabled}
          onClick={() => setIsOpen(true)}
        >
          <TagIcon className="mr-2 h-4 w-4" />
          {selectedTags.length > 0 ? (
            <span>+ Tags ({selectedTags.length})</span>
          ) : (
            <span>+ Tags</span>
          )}
        </Button>

        {/* Mobile Tag Picker Modal */}
        <MobileTagPicker
          selectedTags={selectedTags}
          availableTags={availableTags}
          onTagSelect={onTagSelect}
          onTagRemove={onTagRemove}
          onTagCreate={onTagCreate}
          onSearchTags={onSearchTags}
          placeholder={placeholder}
          disabled={disabled}
          open={isOpen}
          onOpenChange={setIsOpen}
        />
      </div>
    );
  }

  // Desktop version
  return (
    <div className={cn("space-y-2", className)}>
      {/* Selected Tags */}
      {!hideSelectedTags && selectedTags.length > 0 && (
        <div className="flex flex-wrap gap-1.5">
          {selectedTags.map((tag) => (
            <TagPill
              key={tag.id}
              tag={tag}
              onRemove={() => onTagRemove(tag)}
              size="sm"
              showRemove={true}
              allowInlineEdit={false} // Disable inline editing in tag picker to avoid conflicts
            />
          ))}
        </div>
      )}

      {/* Tag Input */}
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "w-full justify-start text-left font-normal",
              selectedTags.length === 0 && "text-muted-foreground",
              disabled && "opacity-50 cursor-not-allowed"
            )}
            disabled={disabled}
          >
            <TagIcon className="mr-2 h-4 w-4" />
            {selectedTags.length > 0 ? (
              <span>+ Tags ({selectedTags.length})</span>
            ) : (
              <span>+ Tags</span>
            )}
          </Button>
        </PopoverTrigger>

        <PopoverContent className="w-80 p-0" align="start">
          <div className="p-3 space-y-3">
            {/* Search Input */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={placeholder}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={handleKeyDown}
                className="pl-9"
                autoFocus={false}
              />
            </div>

            {/* Create New Tag Option */}
            {showCreateOption && (
              <div className="border-b pb-3">
                <div className="flex items-center gap-2 p-2 rounded-md hover:bg-muted/50">
                  <ColorPicker
                    value={newTagColor}
                    onChange={setNewTagColor}
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    className="flex-1 justify-start h-auto p-1"
                    onClick={handleCreateTag}
                    disabled={isCreating}
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Create "{searchTerm}"
                  </Button>
                </div>
              </div>
            )}

            {/* Existing Tags */}
            <div className="max-h-48 overflow-y-auto">
              {isSearching ? (
                <div className="text-center py-4 text-muted-foreground">
                  Searching...
                </div>
              ) : filteredTags.length > 0 ? (
                <div className="space-y-1">
                  {filteredTags.map((tag) => (
                    <Button
                      key={tag.id}
                      variant="ghost"
                      size="sm"
                      className="w-full justify-start h-auto p-2"
                      onClick={() => handleTagSelect(tag)}
                    >
                      <div
                        className="w-3 h-3 rounded-full mr-2 flex-shrink-0"
                        style={{ backgroundColor: tag.color }}
                      />
                      <span className="truncate">{tag.name}</span>
                    </Button>
                  ))}
                </div>
              ) : searchTerm ? (
                <div className="text-center py-4 text-muted-foreground">
                  No tags found
                </div>
              ) : (
                <div className="text-center py-4 text-muted-foreground">
                  Start typing to search or create tags
                </div>
              )}
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
